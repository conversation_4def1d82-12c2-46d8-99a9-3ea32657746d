/**
 * Field Definition Sync Engine v2
 *
 * Handles synchronization of custom field definitions between AutoPatient (AP)
 * and CliniCore (CC) platforms. Provides intelligent field matching, creation
 * of missing fields, and storage of field mappings in the database.
 *
 * @fileoverview v2 Field definition synchronization engine
 * @version 2.0.0
 * @since 2024-08-07
 */

import { eq } from "drizzle-orm";
import apiClient from "@/apiClient";
import { getDb } from "@/database";
import { dbSchema } from "@/database/schema";
import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
} from "@/type/APTypes";
import type { GetCCCustomField, PostCCCustomField } from "@/type/CCTypes";
import { getRequestId } from "@/utils";
import { logDebug, logError, logInfo } from "@/utils/logger";
import { getCcToApMapping } from "../config/fieldMappings";
import { FieldMatcher } from "../core/fieldMatcher";
import { <PERSON><PERSON><PERSON><PERSON>, type TypeCheckOptions } from "../core/typeChecker";
import type {
	APFieldCreationRequest,
	APFieldDataType,
	CCFieldType,
	FieldCreationResult,
	FieldMapping,
	FieldMatchConfig,
	FieldSyncResult,
	SyncError,
	SyncOptions,
} from "../types/index";
import { FieldMatchStrategy, SyncErrorType } from "../types/index";

/**
 * Field Definition Sync Engine
 *
 * Provides comprehensive field definition synchronization between platforms
 * with intelligent matching, type validation, and conflict resolution.
 */
export class FieldDefinitionSync {
	private fieldMatcher: FieldMatcher;
	private typeChecker: TypeChecker;
	private requestId: string;

	constructor(options?: {
		fieldMatchConfig?: Partial<FieldMatchConfig>;
		typeCheckConfig?: Partial<TypeCheckOptions>;
	}) {
		this.fieldMatcher = new FieldMatcher(options?.fieldMatchConfig);
		this.typeChecker = new TypeChecker(options?.typeCheckConfig);
		this.requestId = getRequestId();
	}

	/**
	 * Synchronize field definitions between platforms
	 *
	 * Fetches all custom fields from both platforms, matches existing fields,
	 * creates missing fields (CC → AP only per DATA-TYPE-MAP.md rules),
	 * and stores field mappings in the database.
	 *
	 * @param options - Synchronization options
	 * @param preFetchedFields - Optional pre-fetched fields to use instead of fetching
	 * @returns Comprehensive sync results with detailed field information
	 */
	public async synchronizeFieldDefinitions(
		options: SyncOptions,
		preFetchedFields?: {
			apFields: APGetCustomFieldType[];
			ccFields: GetCCCustomField[];
		}
	): Promise<{
		success: boolean;
		totalFields: { ap: number; cc: number };
		matchedFields: number;
		createdFields: number;
		skippedFields: number;
		failedFields: number;
		results: FieldSyncResult[];
		errors: SyncError[];
		warnings: string[];
		processingTimeMs: number;
	}> {
		const startTime = Date.now();
		const results: FieldSyncResult[] = [];
		const errors: SyncError[] = [];
		const warnings: string[] = [];

		logInfo("Starting field definition synchronization", {
			requestId: this.requestId,
			options,
			usingPreFetchedFields: !!preFetchedFields,
		});

		try {
			// Step 1: Get custom fields from both platforms (either pre-fetched or fetch now)
			const [apFields, ccFields] = preFetchedFields
				? [preFetchedFields.apFields, preFetchedFields.ccFields]
				: await this.fetchAllCustomFields();

			logInfo("Fetched custom fields from both platforms", {
				requestId: this.requestId,
				apFieldCount: apFields.length,
				ccFieldCount: ccFields.length,
			});

			// Step 2: Get existing field mappings from database
			const existingMappings = await this.getExistingFieldMappings();

			logDebug("Retrieved existing field mappings", {
				requestId: this.requestId,
				existingMappingCount: existingMappings.length,
			});

			// Step 3: Match existing fields and update mappings
			const matchResults = await this.matchExistingFields(
				apFields,
				ccFields,
				existingMappings,
				options,
			);

			results.push(...matchResults.results);
			warnings.push(...matchResults.warnings);

			// Step 4: Create missing fields (CC → AP only)
			if (options.createMissingFields) {
				const creationResults = await this.createMissingFields(
					apFields,
					ccFields,
					existingMappings,
					options,
				);

				results.push(...creationResults.results);
				errors.push(...creationResults.errors);
				warnings.push(...creationResults.warnings);
			}

			// Calculate summary statistics
			const matchedFields = results.filter(
				(r) => r.action === "matched",
			).length;
			const createdFields = results.filter(
				(r) => r.action === "created",
			).length;
			const skippedFields = results.filter(
				(r) => r.action === "skipped",
			).length;
			const failedFields = results.filter((r) => r.action === "failed").length;

			const processingTimeMs = Date.now() - startTime;

			logInfo("Field definition synchronization completed", {
				requestId: this.requestId,
				processingTimeMs,
				matchedFields,
				createdFields,
				skippedFields,
				failedFields,
				totalErrors: errors.length,
				totalWarnings: warnings.length,
			});

			return {
				success: errors.length === 0,
				totalFields: { ap: apFields.length, cc: ccFields.length },
				matchedFields,
				createdFields,
				skippedFields,
				failedFields,
				results,
				errors,
				warnings,
				processingTimeMs,
			};
		} catch (error) {
			const syncError: SyncError = {
				type: SyncErrorType.API_ERROR,
				message: `Field definition sync failed: ${error instanceof Error ? error.message : String(error)}`,
				originalError: error instanceof Error ? error : undefined,
			};

			errors.push(syncError);

			logError("Field definition synchronization failed", {
				requestId: this.requestId,
				error: syncError.message,
				processingTimeMs: Date.now() - startTime,
			});

			return {
				success: false,
				totalFields: { ap: 0, cc: 0 },
				matchedFields: 0,
				createdFields: 0,
				skippedFields: 0,
				failedFields: 0,
				results,
				errors,
				warnings,
				processingTimeMs: Date.now() - startTime,
			};
		}
	}

	/**
	 * Fetch all custom fields from both platforms
	 */
	private async fetchAllCustomFields(): Promise<
		[APGetCustomFieldType[], GetCCCustomField[]]
	> {
		try {
			const [apFields, ccFields] = await Promise.all([
				apiClient.ap.apCustomfield.allWithParentFilter(true), // Invalidate cache
				apiClient.cc.ccCustomfieldReq.all(true), // Invalidate cache
			]);

			return [apFields, ccFields];
		} catch (error) {
			logError("Failed to fetch custom fields", {
				requestId: this.requestId,
				error: String(error),
			});
			throw new Error(
				`Failed to fetch custom fields: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * Get existing field mappings from database
	 */
	private async getExistingFieldMappings(): Promise<FieldMapping[]> {
		try {
			const db = getDb();
			const mappings = await db
				.select()
				.from(dbSchema.customFields)
				.where(eq(dbSchema.customFields.mappingType, "custom_to_custom"));

			return mappings.map((mapping) => ({
				id: mapping.id,
				apFieldId: mapping.apId || undefined,
				ccFieldId: mapping.ccId || undefined,
				apFieldName: mapping.name || "",
				ccFieldName: mapping.label || mapping.name || "",
				apFieldType: (mapping.apConfig?.dataType || "TEXT") as APFieldDataType,
				ccFieldType: (mapping.ccConfig?.type || "text") as CCFieldType,
				matchStrategy: FieldMatchStrategy.EXACT, // Default for existing mappings
				confidence: 1.0, // Existing mappings have full confidence
				isStandardField: false,
				isActive: true,
				createdAt: mapping.createdAt,
				updatedAt: mapping.updatedAt,
			}));
		} catch (error) {
			logError("Failed to get existing field mappings", {
				requestId: this.requestId,
				error: String(error),
			});
			throw new Error(
				`Failed to get existing field mappings: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * Match existing fields and update mappings
	 */
	private async matchExistingFields(
		apFields: APGetCustomFieldType[],
		ccFields: GetCCCustomField[],
		existingMappings: FieldMapping[],
		options: SyncOptions,
	): Promise<{
		results: FieldSyncResult[];
		warnings: string[];
	}> {
		const results: FieldSyncResult[] = [];
		const warnings: string[] = [];

		// Create lookup maps for existing mappings
		const apMappingMap = new Map(
			existingMappings.filter((m) => m.apFieldId).map((m) => [m.apFieldId!, m]),
		);
		const ccMappingMap = new Map(
			existingMappings.filter((m) => m.ccFieldId).map((m) => [m.ccFieldId!, m]),
		);

		// Track processed fields to avoid duplicates
		const processedApFields = new Set<string>();
		const processedCcFields = new Set<number>();

		// Process AP fields for matching
		for (const apField of apFields) {
			if (processedApFields.has(apField.id)) continue;

			// Check if already mapped
			const existingMapping = apMappingMap.get(apField.id);
			if (existingMapping) {
				processedApFields.add(apField.id);
				if (existingMapping.ccFieldId) {
					processedCcFields.add(existingMapping.ccFieldId);
				}

				results.push({
					success: true,
					action: "matched",
					apField,
					ccField: ccFields.find((cc) => cc.id === existingMapping.ccFieldId),
					mapping: existingMapping,
				});
				continue;
			}

			// Try to find a match using field matcher
			const matchResult = this.fieldMatcher.findBestMatch(apField, ccFields);

			if (matchResult.matched && matchResult.ccField) {
				const ccField = matchResult.ccField;

				// Check if CC field is already mapped
				if (processedCcFields.has(ccField.id)) {
					warnings.push(
						`CC field "${ccField.name}" already mapped, skipping AP field "${apField.name}"`,
					);
					continue;
				}

				// Store new mapping in database
				if (!options.dryRun) {
					try {
						await this.storeFieldMapping(apField, ccField, matchResult);
						processedApFields.add(apField.id);
						processedCcFields.add(ccField.id);

						results.push({
							success: true,
							action: "matched",
							apField,
							ccField,
							mapping: {
								apFieldId: apField.id,
								ccFieldId: ccField.id,
								apFieldName: apField.name,
								ccFieldName: ccField.label || ccField.name,
								apFieldType: apField.dataType as APFieldDataType,
								ccFieldType: ccField.type as CCFieldType,
								matchStrategy: matchResult.matchType as FieldMatchStrategy,
								confidence: matchResult.confidence,
								isStandardField: false,
								isActive: true,
							},
						});
					} catch (error) {
						results.push({
							success: false,
							action: "failed",
							apField,
							ccField,
							error: `Failed to store mapping: ${error instanceof Error ? error.message : String(error)}`,
						});
					}
				} else {
					// Dry run - just report the match
					results.push({
						success: true,
						action: "matched",
						apField,
						ccField,
						metadata: { dryRun: true },
					});
				}
			}
		}

		return { results, warnings };
	}

	/**
	 * Create missing fields (CC → AP only per DATA-TYPE-MAP.md rules)
	 */
	private async createMissingFields(
		apFields: APGetCustomFieldType[],
		ccFields: GetCCCustomField[],
		existingMappings: FieldMapping[],
		options: SyncOptions,
	): Promise<{
		results: FieldSyncResult[];
		errors: SyncError[];
		warnings: string[];
	}> {
		const results: FieldSyncResult[] = [];
		const errors: SyncError[] = [];
		const warnings: string[] = [];

		// Create lookup maps for existing mappings
		const ccMappingMap = new Map(
			existingMappings.filter((m) => m.ccFieldId).map((m) => [m.ccFieldId!, m]),
		);

		// Find unmapped CC fields that can be created in AP
		const unmappedCcFields = ccFields.filter(
			(ccField) => !ccMappingMap.has(ccField.id),
		);

		logInfo("Processing unmapped CC fields for creation in AP", {
			requestId: this.requestId,
			unmappedCcFieldCount: unmappedCcFields.length,
		});

		for (const ccField of unmappedCcFields) {
			try {
				// Check if field type is supported for CC → AP conversion
				const apMapping = getCcToApMapping(
					ccField.type as CCFieldType,
					ccField.allowMultipleValues,
				);
				if (!apMapping || apMapping.skip) {
					warnings.push(
						`CC field "${ccField.name}" type "${ccField.type}" not supported for AP creation`,
					);
					results.push({
						success: false,
						action: "skipped",
						ccField,
						error: `Field type "${ccField.type}" not supported for AP creation`,
					});
					continue;
				}

				// Check for naming conflicts
				const nameConflict = apFields.find(
					(apField) =>
						apField.name.toLowerCase() === ccField.name.toLowerCase() ||
						apField.fieldKey?.toLowerCase() === ccField.name.toLowerCase(),
				);

				if (nameConflict) {
					warnings.push(
						`AP field name conflict for CC field "${ccField.name}", skipping creation`,
					);
					results.push({
						success: false,
						action: "skipped",
						ccField,
						error: `Name conflict with existing AP field "${nameConflict.name}"`,
					});
					continue;
				}

				// Create field creation request
				const creationRequest = this.generateApFieldCreationRequest(
					ccField,
					apMapping,
				);

				if (!options.dryRun) {
					// Attempt to create the field
					const creationResult = await this.createApField(creationRequest);

					if (creationResult.success && creationResult.field) {
						// Store mapping for newly created field
						await this.storeFieldMapping(
							creationResult.field as APGetCustomFieldType,
							ccField,
							{
								matchType: "created",
								confidence: 1.0,
							},
						);

						results.push({
							success: true,
							action: "created",
							apField: creationResult.field as APGetCustomFieldType,
							ccField,
							mapping: creationResult.mapping,
						});

						logInfo("Successfully created AP field from CC field", {
							requestId: this.requestId,
							ccFieldId: ccField.id,
							ccFieldName: ccField.name,
							apFieldId: (creationResult.field as APGetCustomFieldType).id,
							apFieldName: (creationResult.field as APGetCustomFieldType).name,
						});
					} else {
						const error: SyncError = {
							type: SyncErrorType.API_ERROR,
							message: creationResult.error || "Unknown field creation error",
							field: ccField.name,
							metadata: { ccFieldId: ccField.id },
						};
						errors.push(error);

						results.push({
							success: false,
							action: "failed",
							ccField,
							error: error.message,
						});
					}
				} else {
					// Dry run - just report what would be created
					results.push({
						success: true,
						action: "created",
						ccField,
						metadata: { dryRun: true, creationRequest },
					});
				}
			} catch (error) {
				const syncError: SyncError = {
					type: SyncErrorType.API_ERROR,
					message: `Failed to process CC field "${ccField.name}": ${error instanceof Error ? error.message : String(error)}`,
					field: ccField.name,
					originalError: error instanceof Error ? error : undefined,
					metadata: { ccFieldId: ccField.id },
				};
				errors.push(syncError);

				results.push({
					success: false,
					action: "failed",
					ccField,
					error: syncError.message,
				});
			}
		}

		return { results, errors, warnings };
	}

	/**
	 * Generate AP field creation request from CC field
	 */
	private generateApFieldCreationRequest(
		ccField: GetCCCustomField,
		apMapping: ReturnType<typeof getCcToApMapping>,
	): APFieldCreationRequest {
		if (!apMapping) {
			throw new Error(`No AP mapping found for CC field type: ${ccField.type}`);
		}

		const baseRequest: APPostCustomfieldType = {
			name: ccField.name,
			dataType: apMapping.targetType as APFieldDataType,
			fieldKey: ccField.name.toLowerCase().replace(/[^a-z0-9]/g, "_"),
			showInForms: true,
			model: "contact",
		};

		// Add type-specific configurations
		if (apMapping.targetType === "TEXTBOX_LIST") {
			const mappedOptions = ccField.allowedValues?.map((value) => ({
				label: typeof value === "string" ? value : value.value,
				prefillValue: typeof value === "string" ? value : value.value,
			}));

			// AP API requires at least one option for TEXTBOX_LIST fields
			baseRequest.textBoxListOptions = mappedOptions && mappedOptions.length > 0
				? mappedOptions
				: [
					{
						label: "Value 1",
						prefillValue: "",
					},
				];
		} else if (
			apMapping.targetType === "SINGLE_OPTIONS" ||
			apMapping.targetType === "MULTIPLE_OPTIONS"
		) {
			baseRequest.options =
				ccField.allowedValues?.map((value) => {
					const stringValue = typeof value === "string" ? value : value.value;
					// Ensure the value is a string and trim it to avoid AP API errors
					const cleanValue = String(stringValue).trim();
					return {
						key: cleanValue,
						label: cleanValue,
					};
				}) || [];
		}

		return {
			...baseRequest,
			sourceField: ccField,
			mappingReason: `Created from CC field "${ccField.name}" (${ccField.type})`,
			confidence: 1.0,
		};
	}

	/**
	 * Create AP field from creation request
	 */
	private async createApField(
		request: APFieldCreationRequest,
	): Promise<FieldCreationResult> {
		try {
			// Extract only the valid AP API properties, excluding internal tracking properties
			const { sourceField, mappingReason, confidence, ...apApiPayload } = request;

			const createdField = await apiClient.ap.apCustomfield.create(apApiPayload);

			const mapping: FieldMapping = {
				apFieldId: createdField.id,
				ccFieldId: request.sourceField.id,
				apFieldName: createdField.name,
				ccFieldName: request.sourceField.label || request.sourceField.name,
				apFieldType: createdField.dataType as APFieldDataType,
				ccFieldType: request.sourceField.type as CCFieldType,
				matchStrategy: FieldMatchStrategy.EXACT,
				confidence: request.confidence,
				isStandardField: false,
				isActive: true,
			};

			return {
				success: true,
				field: createdField,
				mapping,
			};
		} catch (error) {
			logError("Failed to create AP field", {
				requestId: this.requestId,
				ccFieldId: request.sourceField.id,
				ccFieldName: request.sourceField.name,
				error: String(error),
			});

			return {
				success: false,
				error: `Failed to create AP field: ${error instanceof Error ? error.message : String(error)}`,
				existingFieldConflict: String(error).includes("already exists"),
				errorDetails: { originalError: error },
			};
		}
	}

	/**
	 * Store field mapping in database
	 */
	private async storeFieldMapping(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
		matchResult: { matchType: string; confidence: number },
	): Promise<void> {
		try {
			const db = getDb();

			const mappingData = {
				apId: apField.id,
				ccId: ccField.id,
				name: apField.name,
				label: ccField.label || ccField.name,
				type: ccField.type,
				apConfig: apField,
				ccConfig: ccField,
				mappingType: "custom_to_custom" as const,
				apStandardField: null,
				ccStandardField: null,
			};

			await db
				.insert(dbSchema.customFields)
				.values(mappingData)
				.onConflictDoUpdate({
					target: [dbSchema.customFields.apId],
					set: {
						ccId: mappingData.ccId,
						name: mappingData.name,
						label: mappingData.label,
						type: mappingData.type,
						apConfig: mappingData.apConfig,
						ccConfig: mappingData.ccConfig,
						updatedAt: new Date(),
					},
				});

			logDebug("Stored field mapping in database", {
				requestId: this.requestId,
				apFieldId: apField.id,
				ccFieldId: ccField.id,
				matchType: matchResult.matchType,
				confidence: matchResult.confidence,
			});
		} catch (error) {
			logError("Failed to store field mapping", {
				requestId: this.requestId,
				apFieldId: apField.id,
				ccFieldId: ccField.id,
				error: String(error),
			});
			throw error;
		}
	}
}
