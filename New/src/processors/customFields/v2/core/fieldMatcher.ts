/**
 * Field Matcher v2
 *
 * Intelligent field matching with real type checking capabilities.
 * Provides exact, normalized, and fuzzy matching strategies with
 * comprehensive type compatibility validation.
 *
 * @fileoverview v2 Field matching with real type checking
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType } from "@/type/APTypes.js";
import type { GetCCCustomField } from "@/type/CCTypes.js";
import { matchString } from "../../../../utils/matchString";
import { isStandardFieldName } from "../config/standardMappings.js";
import type {
	FieldMatchConfig,
	FieldMatchResult,
	TypeCompatibilityResult,
} from "../types/index.js";
import { FieldMatchStrategy } from "../types/index.js";
import { TypeChecker } from "./typeChecker.js";

/**
 * Default field matching configuration
 */
const DEFAULT_MATCH_CONFIG: FieldMatchConfig = {
	strategy: FieldMatchStrategy.NORMALIZED,
	fuzzyThreshold: 0.8,
	normalizeGermanChars: true,
	ignoreCase: true,
	ignoreSpaces: true,
};

/**
 * German character normalization map
 */
const GERMAN_CHAR_MAP: Record<string, string> = {
	ä: "ae",
	ö: "oe",
	ü: "ue",
	ß: "ss",
	Ä: "Ae",
	Ö: "Oe",
	Ü: "Ue",
};

/**
 * Field Matcher class with intelligent matching algorithms
 */
export class FieldMatcher {
	private config: FieldMatchConfig;
	private typeChecker: TypeChecker;

	constructor(config: Partial<FieldMatchConfig> = {}) {
		this.config = { ...DEFAULT_MATCH_CONFIG, ...config };
		this.typeChecker = new TypeChecker();
	}

	/**
	 * Find the best match for an AP field among CC fields
	 */
	public findBestMatch(
		apField: APGetCustomFieldType,
		ccFields: GetCCCustomField[],
	): FieldMatchResult {
		const candidates: FieldMatchResult[] = [];

		for (const ccField of ccFields) {
			const matchResult = this.matchFields(apField, ccField);
			if (matchResult.matched) {
				candidates.push(matchResult);
			}
		}

		// Sort by confidence and type compatibility
		candidates.sort((a, b) => {
			// Prioritize type compatible matches
			if (a.typeCompatible !== b.typeCompatible) {
				return a.typeCompatible ? -1 : 1;
			}
			// Then by confidence
			return b.confidence - a.confidence;
		});

		return (
			candidates[0] || {
				matched: false,
				matchType: "none",
				confidence: 0,
				typeCompatible: false,
				reason: "No compatible match found",
			}
		);
	}

	/**
	 * Find the best match for a CC field among AP fields
	 */
	public findBestMatchReverse(
		ccField: GetCCCustomField,
		apFields: APGetCustomFieldType[],
	): FieldMatchResult {
		const candidates: FieldMatchResult[] = [];

		for (const apField of apFields) {
			const matchResult = this.matchFields(apField, ccField);
			if (matchResult.matched) {
				candidates.push(matchResult);
			}
		}

		// Sort by confidence and type compatibility
		candidates.sort((a, b) => {
			if (a.typeCompatible !== b.typeCompatible) {
				return a.typeCompatible ? -1 : 1;
			}
			return b.confidence - a.confidence;
		});

		return (
			candidates[0] || {
				matched: false,
				matchType: "none",
				confidence: 0,
				typeCompatible: false,
				reason: "No compatible match found",
			}
		);
	}

	/**
	 * Match two fields using configured strategy
	 */
	public matchFields(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
	): FieldMatchResult {
		// Check if either field is a standard field (should be skipped)
		const apFieldName = apField.name || "";
		const ccFieldName = ccField.label || ccField.name || "";

		if (isStandardFieldName(apFieldName) || isStandardFieldName(ccFieldName)) {
			return {
				matched: false,
				matchType: "none",
				confidence: 0,
				typeCompatible: false,
				reason: "Standard field should not be matched as custom field",
			};
		}

		// Check type compatibility first
		const typeCompatibility = this.checkTypeCompatibility(apField, ccField);

		// Try different matching strategies
		let bestMatch: FieldMatchResult = {
			matched: false,
			matchType: "none",
			confidence: 0,
			typeCompatible: typeCompatibility.compatible,
		};

		// Exact match
		const exactMatch = this.exactMatch(apField, ccField);
		if (exactMatch.confidence > bestMatch.confidence) {
			bestMatch = {
				...exactMatch,
				typeCompatible: typeCompatibility.compatible,
			};
		}

		// Normalized match
		const normalizedMatch = this.normalizedMatch(apField, ccField);
		if (normalizedMatch.confidence > bestMatch.confidence) {
			bestMatch = {
				...normalizedMatch,
				typeCompatible: typeCompatibility.compatible,
			};
		}

		// Fuzzy match (only if enabled and no better match found)
		if (
			this.config.strategy === FieldMatchStrategy.FUZZY &&
			bestMatch.confidence < 1.0
		) {
			const fuzzyMatch = this.fuzzyMatch(apField, ccField);
			if (fuzzyMatch.confidence > bestMatch.confidence) {
				bestMatch = {
					...fuzzyMatch,
					typeCompatible: typeCompatibility.compatible,
				};
			}
		}

		// Only consider it a match if confidence meets threshold
		const threshold =
			this.config.strategy === FieldMatchStrategy.FUZZY
				? this.config.fuzzyThreshold || 0.8
				: 0.9;

		if (bestMatch.confidence >= threshold) {
			bestMatch.matched = true;
			bestMatch.apField = apField;
			bestMatch.ccField = ccField;
		} else {
			bestMatch.matched = false;
			bestMatch.reason = `Confidence ${bestMatch.confidence.toFixed(2)} below threshold ${threshold}`;
		}

		return bestMatch;
	}

	/**
	 * Exact field name matching using matchString utility
	 */
	private exactMatch(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
	): Omit<FieldMatchResult, "typeCompatible"> {
		const apNames = this.getApFieldNames(apField);
		const ccNames = this.getCcFieldNames(ccField);

		// Check if any AP field name matches any CC field name using matchString
		for (const apName of apNames) {
			for (const ccName of ccNames) {
				if (matchString(apName, ccName)) {
					return {
						matched: true,
						matchType: FieldMatchStrategy.EXACT,
						confidence: 1.0,
					};
				}
			}
		}

		return {
			matched: false,
			matchType: "none",
			confidence: 0,
		};
	}

	/**
	 * Normalized field name matching using matchString utility
	 * Note: matchString already performs normalization, so this is essentially the same as exactMatch
	 */
	private normalizedMatch(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
	): Omit<FieldMatchResult, "typeCompatible"> {
		const apNames = this.getApFieldNames(apField);
		const ccNames = this.getCcFieldNames(ccField);

		// Check if any AP field name matches any CC field name using matchString
		// matchString already handles normalization (removes non-alphanumeric, lowercase)
		for (const apName of apNames) {
			for (const ccName of ccNames) {
				if (matchString(apName, ccName)) {
					return {
						matched: true,
						matchType: FieldMatchStrategy.NORMALIZED,
						confidence: 0.95,
					};
				}
			}
		}

		return {
			matched: false,
			matchType: "none",
			confidence: 0,
		};
	}

	/**
	 * Fuzzy field name matching using Levenshtein distance
	 */
	private fuzzyMatch(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
	): Omit<FieldMatchResult, "typeCompatible"> {
		const apName = this.normalizeFieldName(this.getFieldName(apField));
		const ccName = this.normalizeFieldName(this.getFieldName(ccField));

		const similarity = this.calculateSimilarity(apName, ccName);
		const threshold = this.config.fuzzyThreshold || 0.8;

		if (similarity >= threshold) {
			return {
				matched: true,
				matchType: FieldMatchStrategy.FUZZY,
				confidence: similarity,
			};
		}

		return {
			matched: false,
			matchType: "none",
			confidence: similarity,
		};
	}

	/**
	 * Check type compatibility between AP and CC fields
	 */
	private checkTypeCompatibility(
		apField: APGetCustomFieldType,
		ccField: GetCCCustomField,
	): TypeCompatibilityResult {
		const result = this.typeChecker.checkCompatibility(
			apField.dataType as import("../types/index.js").APFieldDataType,
			ccField.type as import("../types/index.js").CCFieldType,
			undefined, // AP fields don't have allowMultipleValues
			ccField.allowMultipleValues,
		);

		// Convert DetailedTypeCompatibility to TypeCompatibilityResult
		return {
			compatible: result.compatible,
			reason: result.reason,
			requiresConversion: result.requiresSpecialHandling,
			conversionNotes: result.conversionNotes,
		};
	}

	/**
	 * Get field name with fallback to label
	 */
	private getFieldName(field: APGetCustomFieldType | GetCCCustomField): string {
		if ("label" in field && field.label) {
			return field.label;
		}
		return field.name || "";
	}

	/**
	 * Get all possible names for an AP field (name and fieldKey)
	 */
	private getApFieldNames(apField: APGetCustomFieldType): string[] {
		const names: string[] = [];

		if (apField.name) {
			names.push(apField.name);
		}

		if (apField.fieldKey && apField.fieldKey !== apField.name) {
			names.push(apField.fieldKey);
		}

		return names;
	}

	/**
	 * Get all possible names for a CC field (name and label)
	 */
	private getCcFieldNames(ccField: GetCCCustomField): string[] {
		const names: string[] = [];

		if (ccField.name) {
			names.push(ccField.name);
		}

		if (ccField.label && ccField.label !== ccField.name) {
			names.push(ccField.label);
		}

		return names;
	}

	/**
	 * Normalize field name for comparison
	 */
	private normalizeFieldName(name: string): string {
		let normalized = name;

		// Convert to lowercase if configured
		if (this.config.ignoreCase) {
			normalized = normalized.toLowerCase();
		}

		// Remove spaces if configured
		if (this.config.ignoreSpaces) {
			normalized = normalized.replace(/\s+/g, "");
		}

		// Normalize German characters if configured
		if (this.config.normalizeGermanChars) {
			for (const [german, replacement] of Object.entries(GERMAN_CHAR_MAP)) {
				normalized = normalized.replace(new RegExp(german, "g"), replacement);
			}
		}

		// Remove special characters and normalize
		normalized = normalized
			.replace(/[^\w\s]/g, "") // Remove special chars
			.replace(/\s+/g, "") // Remove remaining spaces
			.trim();

		return normalized;
	}

	/**
	 * Calculate similarity between two strings using Levenshtein distance
	 */
	private calculateSimilarity(str1: string, str2: string): number {
		if (str1 === str2) return 1.0;
		if (str1.length === 0 || str2.length === 0) return 0.0;

		const distance = this.levenshteinDistance(str1, str2);
		const maxLength = Math.max(str1.length, str2.length);

		return 1 - distance / maxLength;
	}

	/**
	 * Calculate Levenshtein distance between two strings
	 */
	private levenshteinDistance(str1: string, str2: string): number {
		const matrix: number[][] = [];

		// Initialize matrix
		for (let i = 0; i <= str2.length; i++) {
			matrix[i] = [i];
		}
		for (let j = 0; j <= str1.length; j++) {
			matrix[0][j] = j;
		}

		// Fill matrix
		for (let i = 1; i <= str2.length; i++) {
			for (let j = 1; j <= str1.length; j++) {
				if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
					matrix[i][j] = matrix[i - 1][j - 1];
				} else {
					matrix[i][j] = Math.min(
						matrix[i - 1][j - 1] + 1, // substitution
						matrix[i][j - 1] + 1, // insertion
						matrix[i - 1][j] + 1, // deletion
					);
				}
			}
		}

		return matrix[str2.length][str1.length];
	}
}
